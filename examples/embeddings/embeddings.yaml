# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

apiVersion: gateway.networking.k8s.io/v1
kind: GatewayClass
metadata:
  name: ai-gateway-embeddings
spec:
  controllerName: gateway.envoyproxy.io/gatewayclass-controller
---
apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: ai-gateway-embeddings
  namespace: default
spec:
  gatewayClassName: ai-gateway-embeddings
  listeners:
    - name: http
      protocol: HTTP
      port: 80
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: ai-gateway-embeddings
  namespace: default
spec:
  schema:
    name: OpenAI
  targetRefs:
    - name: ai-gateway-embeddings
      kind: Gateway
      group: gateway.networking.k8s.io
  rules:
    # OpenAI text-embedding-ada-002 (most popular)
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: text-embedding-ada-002
      backendRefs:
        - name: openai-embeddings-backend
    # OpenAI text-embedding-3-small (newer, improved)
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: text-embedding-3-small
      backendRefs:
        - name: openai-embeddings-backend
    # OpenAI text-embedding-3-large (highest performance)
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: text-embedding-3-large
      backendRefs:
        - name: openai-embeddings-backend
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: openai-embeddings-backend
  namespace: default
spec:
  schema:
    name: OpenAI
    version: v1
  backendRef:
    name: openai-embeddings-backend
    kind: Backend
    group: gateway.envoyproxy.io
  backendSecurityPolicyRef:
    name: openai-embeddings-apikey
    kind: BackendSecurityPolicy
    group: aigateway.envoyproxy.io
---
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: BackendSecurityPolicy
metadata:
  name: openai-embeddings-apikey
  namespace: default
spec:
  type: APIKey
  apiKey:
    secretRef:
      name: openai-embeddings-apikey
      namespace: default
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: Backend
metadata:
  name: openai-embeddings-backend
  namespace: default
spec:
  endpoints:
    - fqdn:
        hostname: api.openai.com
        port: 443
---
apiVersion: gateway.networking.k8s.io/v1alpha3
kind: BackendTLSPolicy
metadata:
  name: openai-embeddings-tls
  namespace: default
spec:
  targetRefs:
    - group: 'gateway.envoyproxy.io'
      kind: Backend
      name: openai-embeddings-backend
  validation:
    wellKnownCACertificates: "System"
    hostname: api.openai.com
---
apiVersion: v1
kind: Secret
metadata:
  name: openai-embeddings-apikey
  namespace: default
type: Opaque
stringData:
  apiKey: OPENAI_API_KEY  # Replace with your OpenAI API key
