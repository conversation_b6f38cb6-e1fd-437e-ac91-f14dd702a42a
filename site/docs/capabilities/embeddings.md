---
id: embeddings
title: Embeddings Support
sidebar_position: 5
---

# Embeddings Support

Envoy AI Gateway provides comprehensive support for text embeddings through the OpenAI-compatible `/v1/embeddings` endpoint. This enables you to generate vector representations of text for use cases like semantic search, similarity matching, and retrieval-augmented generation (RAG).

## Overview

The embeddings endpoint allows you to:

- Convert text into high-dimensional vector representations
- Support both single text inputs and batch processing
- Route requests to different embedding models based on configuration
- Monitor token usage and performance metrics
- Handle authentication and rate limiting

## Supported Providers

The following providers support embeddings through Envoy AI Gateway:

| Provider | Embeddings Support | Notes |
|----------|-------------------|-------|
| OpenAI | ✅ | Full support for all OpenAI embedding models |
| Google Gemini on AI Studio | ✅ | Via OpenAI-compatible endpoint |
| Groq | ✅ | Via OpenAI-compatible endpoint |
| Together AI | ✅ | Via OpenAI-compatible endpoint |
| Mistral | ✅ | Via OpenAI-compatible endpoint |
| DeepInfra | ✅ | Via OpenAI-compatible endpoint |
| DeepSeek | ✅ | Via OpenAI-compatible endpoint |
| AWS Bedrock | ❌ | Not yet supported |
| Azure OpenAI | ❌ | Not yet supported |

## API Usage

### Basic Request

```bash
curl -H "Content-Type: application/json" \
  -d '{
    "model": "text-embedding-ada-002",
    "input": "Hello, world!"
  }' \
  $GATEWAY_URL/v1/embeddings
```

### Batch Request

```bash
curl -H "Content-Type: application/json" \
  -d '{
    "model": "text-embedding-ada-002",
    "input": ["Hello, world!", "How are you?", "Goodbye!"]
  }' \
  $GATEWAY_URL/v1/embeddings
```

### Response Format

```json
{
  "object": "list",
  "data": [
    {
      "object": "embedding",
      "embedding": [0.1, 0.2, 0.3, ...],
      "index": 0
    }
  ],
  "model": "text-embedding-ada-002",
  "usage": {
    "prompt_tokens": 3,
    "total_tokens": 3
  }
}
```

## Configuration

### Model Routing

Configure different embedding models using AIGatewayRoute:

```yaml
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIGatewayRoute
metadata:
  name: embeddings-route
  namespace: default
spec:
  schema:
    name: OpenAI
  targetRefs:
    - name: ai-gateway
      kind: Gateway
      group: gateway.networking.k8s.io
  rules:
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: text-embedding-ada-002
      backendRefs:
        - name: openai-backend
    - matches:
        - headers:
            - type: Exact
              name: x-ai-eg-model
              value: text-embedding-3-small
      backendRefs:
        - name: openai-backend
```

### Backend Configuration

```yaml
apiVersion: aigateway.envoyproxy.io/v1alpha1
kind: AIServiceBackend
metadata:
  name: openai-backend
  namespace: default
spec:
  schema:
    name: OpenAI
    version: v1
  backendRef:
    name: openai-backend
    kind: Backend
    group: gateway.envoyproxy.io
  backendSecurityPolicyRef:
    name: openai-api-key
    kind: BackendSecurityPolicy
    group: aigateway.envoyproxy.io
```

## Features

### Token Usage Tracking

Envoy AI Gateway automatically tracks token usage for embeddings requests:

- **Input tokens**: Number of tokens in the input text
- **Total tokens**: Same as input tokens for embeddings (no output tokens)

### Metrics and Monitoring

The gateway provides comprehensive metrics for embeddings:

- Request count and success/failure rates
- Token usage statistics
- Response latency
- Model-specific metrics

### Error Handling

The gateway handles various error scenarios:

- Invalid input format
- Model not found
- Rate limiting
- Upstream service errors

All errors are returned in OpenAI-compatible format for consistency.

## Popular Embedding Models

### OpenAI Models

- `text-embedding-ada-002`: Most popular, good balance of performance and cost
- `text-embedding-3-small`: Newer model with improved performance
- `text-embedding-3-large`: Highest performance for demanding applications

### Configuration Example

```yaml
# Add multiple embedding models
rules:
  - matches:
      - headers:
          - type: Exact
            name: x-ai-eg-model
            value: text-embedding-ada-002
    backendRefs:
      - name: openai-backend
  - matches:
      - headers:
          - type: Exact
            name: x-ai-eg-model
            value: text-embedding-3-small
    backendRefs:
      - name: openai-backend
  - matches:
      - headers:
          - type: Exact
            name: x-ai-eg-model
            value: text-embedding-3-large
    backendRefs:
      - name: openai-backend
```

## Best Practices

1. **Choose the right model**: Balance performance needs with cost considerations
2. **Batch requests**: Use batch processing for multiple texts to improve efficiency
3. **Monitor usage**: Track token consumption to manage costs
4. **Handle errors**: Implement proper error handling for production applications
5. **Cache results**: Consider caching embeddings for frequently used texts

## Next Steps

- [Connect to OpenAI](../getting-started/connect-providers/openai.md) for embedding models
- [Configure metrics](./metrics.md) to monitor embedding usage
- [Set up rate limiting](./usage-based-ratelimiting.md) to control costs
